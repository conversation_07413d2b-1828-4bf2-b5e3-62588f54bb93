package routes

import (
	"github.com/SneatX/phillips_go/internal/adapters/http/handlers"
	jwt "github.com/SneatX/phillips_go/internal/platform/token"
	"github.com/gin-gonic/gin"
)

func RegisterAllRoutes(
	api *gin.RouterGroup,
	jwtManager *jwt.JWTManager,
	userHandler *handlers.UserHandler,
	authHandler *handlers.AuthHandler,
	assignmentHandler *handlers.AssignmentHandler,
	locationHandler *handlers.LocationHandler,
	contactHandler *handlers.ContactHandler,
	adjusterHandler *handlers.AdjusterHandler,
	categoryHandler *handlers.CategoryHandler,
	carrierHandler *handlers.<PERSON><PERSON><PERSON><PERSON>,
	consultantHandler *handlers.ConsultantHandler,
) {
	RegisterAuthRoutes(api, jwtManager, authHandler)
	RegisterUserRoutes(api, jwtManager, userHandler)
	RegisterAssignmentRoutes(api, assignmentHandler, jwtManager)
	RegisterLocationRoutes(api, locationHandler, jwtManager)
	RegisterContactRoutes(api, contactHandler, jwtManager)
	RegisterAdjusterRoutes(api, adjuster<PERSON>andler, jwtManager)
	RegisterCategoryRoutes(api, categoryHandler, jwtManager)
	RegisterCarrierRoutes(api, carrierHandler, jwtManager)
	RegisterConsultantRoutes(api, consultantHandler, jwtManager)
}
