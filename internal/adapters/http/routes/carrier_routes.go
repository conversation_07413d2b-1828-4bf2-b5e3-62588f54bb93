package routes

import (
	"github.com/SneatX/phillips_go/internal/adapters/http/handlers"
	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	jwt "github.com/SneatX/phillips_go/internal/platform/token"
	"github.com/gin-gonic/gin"
)

func RegisterCarrierRoutes(rg *gin.RouterGroup, handler *handlers.CarrierHandler, jwtManager *jwt.JWTManager) {
	publicCarriers := rg.Group("/carriers")

	protectedCarriers := publicCarriers.Group("", middleware.JWTAuthMiddleware(jwtManager))
	{
		protectedCarriers.GET("", handler.GetAllCarriers)
		protectedCarriers.POST("", handler.CreateCarrier)
		protectedCarriers.PUT("/:id", handler.UpdateCarrier)
	}
}
