package routes

import (
	"github.com/SneatX/phillips_go/internal/adapters/http/handlers"
	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	jwt "github.com/SneatX/phillips_go/internal/platform/token"
	"github.com/gin-gonic/gin"
)

func RegisterContactRoutes(rg *gin.RouterGroup, handler *handlers.ContactHandler, jwtManager *jwt.JWTManager) {
	publicContacts := rg.Group("/contacts")

	protectedContacts := publicContacts.Group("", middleware.JWTAuthMiddleware(jwtManager))
	{
		protectedContacts.POST("", handler.CreateContact)
		protectedContacts.PUT("/:id", handler.UpdateContact)
		protectedContacts.DELETE("/:id", handler.DeleteContact)
	}
}
