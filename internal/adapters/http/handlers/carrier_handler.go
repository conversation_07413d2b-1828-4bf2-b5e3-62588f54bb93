package handlers

import (
	"strconv"

	"github.com/SneatX/phillips_go/internal/adapters/http/response"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/dto"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/gin-gonic/gin"
)

type CarrierHandler struct {
	carrierService in.CarrierService
}

func NewCarrierHandler(service in.CarrierService) *CarrierHandler {
	return &CarrierHandler{carrierService: service}
}

func (h *CarrierHandler) GetAllCarriers(c *gin.Context) {
	carriers, err := h.carrierService.GetAll()
	if err != nil {
		_ = c.Error(err)
		return
	}
	response.SuccessMany(c, dto.NewCarrierListResponse(carriers, "Carriers retrieved successfully"))
}

func (h *CarrierHandler) CreateCarrier(c *gin.Context) {
	var req dto.CreateCarrierRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	carrierToCreate := dto.ToDomainCarrier(&req)
	createdCarrier, err := h.carrierService.Create(carrierToCreate)
	if err != nil {
		_ = c.Error(err)
		return
	}
	response.SuccessOne(c, dto.FromDomainCarrier(*createdCarrier))
}

func (h *CarrierHandler) UpdateCarrier(c *gin.Context) {

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)

	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid carrier ID format. Must be a positive integer."))
		return
	}

	var req dto.UpdateCarrierRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	carrierToUpdate := dto.ToUpdateDomainCarrier(&req, uint(id))
	updatedCarrier, err := h.carrierService.Update(carrierToUpdate)
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainCarrier(*updatedCarrier))
}
