package handlers

import (
	"strconv"

	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	"github.com/SneatX/phillips_go/internal/adapters/http/response"
	"github.com/SneatX/phillips_go/internal/dto"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"

	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/gin-gonic/gin"
)

type ContactHandler struct {
	contactService in.ContactService
}

func NewContactHandler(service in.ContactService) *ContactHandler {
	return &ContactHandler{
		contactService: service,
	}
}

func (h *ContactHandler) CreateContact(c *gin.Context) {
	_, exists := middleware.GetUserClaimsFromContext(c)
	if !exists {
		response.ErrorResponse(c, appErrors.NewUnauthorizedError("User claims not found in context. Authentication required."))
		return
	}

	var req dto.CreateContactRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	contactToCreate := dto.ToDomainContact(&req)
	createdContact, err := h.contactService.Create(contactToCreate)
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainContact(*createdContact))
}

func (h *ContactHandler) UpdateContact(c *gin.Context) {
	_, exists := middleware.GetUserClaimsFromContext(c)
	if !exists {
		response.ErrorResponse(c, appErrors.NewUnauthorizedError("User claims not found in context. Authentication required."))
		return
	}

	// Get contact ID from URL parameter
	contactIDStr := c.Param("id")
	contactID, err := strconv.ParseUint(contactIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid contact ID", err))
		return
	}

	// Check if contact exists
	_, err = h.contactService.GetByID(uint(contactID))
	if err != nil {
		_ = c.Error(err)
		return
	}

	var req dto.UpdateContactRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	contactToUpdate := dto.ToUpdateDomainContact(&req, uint(contactID))
	updatedContact, err := h.contactService.Update(contactToUpdate)
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainContact(*updatedContact))
}

func (h *ContactHandler) DeleteContact(c *gin.Context) {
	_, exists := middleware.GetUserClaimsFromContext(c)
	if !exists {
		response.ErrorResponse(c, appErrors.NewUnauthorizedError("User claims not found in context. Authentication required."))
		return
	}

	// Get contact ID from URL parameter
	contactIDStr := c.Param("id")
	contactID, err := strconv.ParseUint(contactIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid contact ID", err))
		return
	}

	// Check if contact exists
	_, err = h.contactService.GetByID(uint(contactID))
	if err != nil {
		_ = c.Error(err)
		return
	}

	err = h.contactService.Delete(uint(contactID))
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, gin.H{"message": "Contact deleted successfully"})
}
