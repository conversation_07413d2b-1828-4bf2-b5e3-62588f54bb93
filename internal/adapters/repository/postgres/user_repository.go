package postgres

import (
	"errors"
	"fmt"

	"github.com/SneatX/phillips_go/internal/core/domain"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/jackc/pgx/v5/pgconn"
	"gorm.io/gorm"
)

type UserRepository struct {
	DB *gorm.DB
}

func NewUserRepository(DB *gorm.DB) *UserRepository {
	return &UserRepository{DB: DB}
}

func (r *UserRepository) GetByID(id uint) (*domain.User, error) {
	var user domain.User
	if err := r.DB.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, appErrors.NewNotFoundError(fmt.Sprintf("user with ID %d could not be found at service layer", id), err)
		}
		return nil, appErrors.NewInternalError(fmt.Sprintf("failed to retrieve user %d due to unexpected issue", id), err)
	}
	return &user, nil
}

func (r *UserRepository) GetByEmail(email string) (*domain.User, error) {
	var user domain.User
	if err := r.DB.Where("email = ?", email).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, appErrors.NewNotFoundError(fmt.Sprintf("user with email %s could not be found", email), err)
		}
		return nil, appErrors.NewInternalError(fmt.Sprintf("failed to retrieve user with email %s due to unexpected issue", email), err)
	}
	return &user, nil
}

func (r *UserRepository) GetAll() ([]domain.User, error) {
	var users []domain.User
	if err := r.DB.Find(&users).Error; err != nil {
		return nil, appErrors.NewInternalError("failed to retrieve all users from database", err)
	}
	return users, nil
}

func (r *UserRepository) Create(user domain.User) (*domain.User, error) {
	if result := r.DB.Create(&user); result.Error != nil {
		var pgErr *pgconn.PgError
		if errors.As(result.Error, &pgErr) {
			fmt.Printf("PostgreSQL error code: %s, message: %s\n", pgErr.Code, pgErr.Message)
			if pgErr.Code == "23505" {
				return nil, appErrors.NewConflictError("user with this email already exists")
			}
		}
		return nil, appErrors.NewInternalError(fmt.Sprintf("failed to create user due to unexpected database error: %s", result.Error.Error()))
	}
	return &user, nil
}
