package postgres

import (
	"errors"
	"fmt"

	"github.com/SneatX/phillips_go/internal/core/domain"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"gorm.io/gorm"
)

type contactRepository struct {
	DB *gorm.DB
}

func NewContactRepository(DB *gorm.DB) *contactRepository {
	return &contactRepository{DB: DB}
}

func (r *contactRepository) GetByID(id uint) (*domain.Contact, error) {
	var contact domain.Contact

	if err := r.DB.First(&contact, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, appErrors.NewNotFoundError(fmt.Sprintf("contact with ID %d could not be found", id), err)
		}
		return nil, appErrors.NewInternalError(fmt.Sprintf("failed to retrieve contact %d due to unexpected issue", id), err)
	}

	return &contact, nil
}

func (r *contactRepository) Create(contact *domain.Contact) (*domain.Contact, error) {
	if result := r.DB.Create(&contact); result.Error != nil {
		return nil, appErrors.NewInternalError("failed to create contact", result.Error)
	}
	return contact, nil
}

func (r *contactRepository) Update(contact *domain.Contact) (*domain.Contact, error) {
	if result := r.DB.Save(&contact); result.Error != nil {
		return nil, appErrors.NewInternalError("failed to update contact", result.Error)
	}
	return contact, nil
}

func (r *contactRepository) Delete(id uint) error {
	if result := r.DB.Delete(&domain.Contact{}, id); result.Error != nil {
		return appErrors.NewInternalError("failed to delete contact", result.Error)
	}
	return nil
}
