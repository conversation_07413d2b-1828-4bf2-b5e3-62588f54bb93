package postgres

import (
	"errors"
	"fmt"

	"github.com/SneatX/phillips_go/internal/core/domain"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/jackc/pgx/v5/pgconn"
	"gorm.io/gorm"
)

type adjusterRepository struct {
	DB *gorm.DB
}

func NewAdjusterRepository(DB *gorm.DB) *adjusterRepository {
	return &adjusterRepository{DB: DB}
}

func (r *adjusterRepository) GetAll() ([]*domain.Adjuster, error) {
	var adjusters []*domain.Adjuster
	if err := r.DB.
		Preload("Carriers").
		Find(&adjusters).Error; err != nil {
		return nil, appErrors.NewInternalError("failed to retrieve all adjusters from database", err)
	}
	return adjusters, nil
}

func (r *adjusterRepository) GetByID(id uint) (*domain.Adjuster, error) {
	var adjuster *domain.Adjuster

	if err := r.DB.
		Preload("Carriers").
		First(&adjuster, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, appErrors.NewNotFoundError(fmt.Sprintf("adjuster with ID %d could not be found", id), err)
		}
		return nil, appErrors.NewInternalError(fmt.Sprintf("Failed to retrieve adjuster %d due to unexpected issue", id), err)
	}

	return adjuster, nil
}

func (r *adjusterRepository) Create(adjuster *domain.Adjuster) (*domain.Adjuster, error) {
	result := r.DB.Create(&adjuster)

	if result.Error != nil {
		var pgErr *pgconn.PgError
		if errors.As(result.Error, &pgErr) {
			fmt.Printf("PostgreSQL error code: %s, message: %s\n", pgErr.Code, pgErr.Message)
			if pgErr.Code == "23505" {
				return nil, appErrors.NewConflictError(fmt.Sprintf("New conflict error, %s", result.Error))
			}
			if pgErr.Code == "23503" {
				return nil, appErrors.NewNotFoundError("foreign key constraint violation, please check that all related entities exist before creating an adjuster")
			}
		}
	}

	if err := r.DB.Preload("Carriers").First(&adjuster, adjuster.ID).Error; err != nil {
		return nil, appErrors.NewInternalError("failed to load created adjuster", err)
	}

	return adjuster, nil
}

func (r *adjusterRepository) Update(adjuster *domain.Adjuster) (*domain.Adjuster, error) {
	var newCarriers []*domain.Carrier
	var existing *domain.Adjuster

	if err := r.DB.First(&existing, adjuster.ID).Error; err != nil {
		return nil, appErrors.NewNotFoundError("Adjuster not found")
	}

	if len(adjuster.Carriers) > 0 {
		carrierIDs := make([]uint, len(adjuster.Carriers))
		for i, carrier := range adjuster.Carriers {
			carrierIDs[i] = carrier.ID
		}
		r.DB.Where("id IN ?", carrierIDs).Find(&newCarriers)
	}

	if err := r.DB.Model(&existing).
		Association("Carriers").
		Replace(&newCarriers); err != nil {
		return nil, appErrors.NewInternalError("Failed to update adjuster carriers", err)
	}

	result := r.DB.Model(&existing).Updates(adjuster)

	if err := result.Error; err != nil {
		var pgErr *pgconn.PgError
		if errors.As(err, &pgErr) {
			fmt.Printf("PostgreSQL error code: %s, message: %s\n", pgErr.Code, pgErr.Message)
			if pgErr.Code == "23505" {
				return nil, appErrors.NewConflictError(fmt.Sprintf("New conflict error, %s", err))
			}
			if pgErr.Code == "23503" {
				return nil, appErrors.NewNotFoundError("foreign key constraint violation, please check that all related entities exist before updating an assignment")
			}
		}
		return nil, appErrors.NewInternalError("failed to update assignment", err)
	}
	return existing, nil
}
