package service

import (
	"github.com/SneatX/phillips_go/internal/core/domain"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/core/port/out"
)

type assignmentService struct {
	assignmentRepo out.AssignmentRepository
	categoryRepo   out.CategoryRepository
}

func NewAssignmentService(repo out.AssignmentRepository, categoryRepo out.CategoryRepository) in.AssignmentService {
	return &assignmentService{
		assignmentRepo: repo,
		categoryRepo:   categoryRepo,
	}
}

func (s *assignmentService) GetAll() ([]*domain.Assignment, error) {
	return s.assignmentRepo.GetAll()
}

func (s *assignmentService) GetByUserId(userID uint) ([]*domain.Assignment, error) {
	return s.assignmentRepo.GetByUserId(userID)
}

func (s *assignmentService) GetByID(id uint) (*domain.Assignment, error) {
	return s.assignmentRepo.GetByID(id)
}

func (s *assignmentService) Create(assignment *domain.Assignment) (*domain.Assignment, error) {
	result, err := s.assignmentRepo.Create(assignment)
	if err != nil {
		return nil, err
	}

	categories, err := s.categoryRepo.GetAll()
	if err != nil {
		// Don't fail the assignment creation if we can't get categories
		return result, nil
	}

	for _, category := range categories {
		assignmentCategory := &domain.AssignmentCategory{
			AssignmentID:    result.ID,
			CategoryID:      category.ID,
			PresentValue:    0.0,
			AsAnalyzedValue: 0.0,
		}
		s.categoryRepo.CreateAssignmentCategory(assignmentCategory)
	}

	return result, nil
}

func (s *assignmentService) Update(assignment *domain.Assignment) (*domain.Assignment, error) {
	return s.assignmentRepo.Update(assignment)
}
