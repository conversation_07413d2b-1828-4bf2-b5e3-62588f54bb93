package service

import (
	"github.com/SneatX/phillips_go/internal/core/domain"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/core/port/out"
)

type locationService struct {
	locationRepo out.LocationRepository
}

func NewLocationService(repo out.LocationRepository) in.LocationService {
	return &locationService{locationRepo: repo}
}

func (s *locationService) GetByID(id uint) (*domain.Location, error) {
	return s.locationRepo.GetByID(id)
}

func (s *locationService) Create(location *domain.Location) (*domain.Location, error) {
	return s.locationRepo.Create(location)
}

func (s *locationService) Update(location *domain.Location) (*domain.Location, error) {
	return s.locationRepo.Update(location)
}

func (s *locationService) Delete(id uint) error {
	return s.locationRepo.Delete(id)
}
