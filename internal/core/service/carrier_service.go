package service

import (
	"github.com/SneatX/phillips_go/internal/core/domain"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/core/port/out"
)

type carrierService struct {
	carrierRepo out.CarrierRepository
}

func NewCarrierService(repo out.CarrierRepository) in.CarrierService {
	return &carrierService{carrierRepo: repo}
}

func (s *carrierService) GetAll() ([]*domain.Carrier, error) {
	return s.carrierRepo.GetAll()
}

func (s *carrierService) Create(carrier *domain.Carrier) (*domain.Carrier, error) {
	return s.carrierRepo.Create(carrier)
}

func (s *carrierService) Update(carrier *domain.Carrier) (*domain.Carrier, error) {
	return s.carrierRepo.Update(carrier)
}
