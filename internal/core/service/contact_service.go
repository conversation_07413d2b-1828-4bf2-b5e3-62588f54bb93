package service

import (
	"github.com/SneatX/phillips_go/internal/core/domain"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/core/port/out"
)

type contactService struct {
	contactRepo out.ContactRepository
}

func NewContactService(repo out.ContactRepository) in.ContactService {
	return &contactService{contactRepo: repo}
}

func (s *contactService) GetByID(id uint) (*domain.Contact, error) {
	return s.contactRepo.GetByID(id)
}

func (s *contactService) Create(contact *domain.Contact) (*domain.Contact, error) {
	return s.contactRepo.Create(contact)
}

func (s *contactService) Update(contact *domain.Contact) (*domain.Contact, error) {
	return s.contactRepo.Update(contact)
}

func (s *contactService) Delete(id uint) error {
	return s.contactRepo.Delete(id)
}
