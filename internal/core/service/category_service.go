package service

import (
	"github.com/SneatX/phillips_go/internal/core/domain"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/core/port/out"
)

type categoryService struct {
	categoryRepo out.CategoryRepository
}

func NewCategoryService(repo out.CategoryRepository) in.CategoryService {
	return &categoryService{categoryRepo: repo}
}

func (s *categoryService) GetAll() ([]*domain.Category, error) {
	return s.categoryRepo.GetAll()
}

func (s *categoryService) Create(category *domain.Category) (*domain.Category, error) {
	return s.categoryRepo.Create(category)
}

func (s *categoryService) Update(category *domain.Category) (*domain.Category, error) {
	return s.categoryRepo.Update(category)
}

func (s *categoryService) CreateAssignmentCategory(assignmentCategory *domain.AssignmentCategory) (*domain.AssignmentCategory, error) {
	return s.categoryRepo.CreateAssignmentCategory(assignmentCategory)
}

func (s *categoryService) PatchAssignmentCategory(assignmentCategory *domain.AssignmentCategory) (*domain.AssignmentCategory, error) {
	return s.categoryRepo.PatchAssignmentCategory(assignmentCategory)
}
