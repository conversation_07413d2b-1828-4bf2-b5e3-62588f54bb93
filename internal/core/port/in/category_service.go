package in

import "github.com/SneatX/phillips_go/internal/core/domain"

type CategoryService interface {
	GetAll() ([]*domain.Category, error)
	Create(*domain.Category) (*domain.Category, error)
	Update(*domain.Category) (*domain.Category, error)
	CreateAssignmentCategory(*domain.AssignmentCategory) (*domain.AssignmentCategory, error)
	PatchAssignmentCategory(*domain.AssignmentCategory) (*domain.AssignmentCategory, error)
}
