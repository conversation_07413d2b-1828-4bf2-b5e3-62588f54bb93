package domain

import "time"

type Carrier struct {
	ID         uint     `gorm:"primaryKey;autoIncrement" json:"id"`
	Name       string   `gorm:"size:255;not null" json:"name"`
	Email      string   `gorm:"size:255;not null" json:"email"`
	Phone      string   `gorm:"size:50;not null" json:"phone"`
	AdjusterID *uint    `gorm:"index" json:"adjusterId"`
	Adjuster   Adjuster `gorm:"constraint:OnUpdate:CASCADE,OnDelete:SET NULL;" json:"adjuster"`
	CreatedAt  time.Time
	UpdatedAt  time.Time
}
