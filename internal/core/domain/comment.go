package domain

import "time"

type Comment struct {
	ID           uint       `gorm:"primaryKey;autoIncrement" json:"id"`
	Consultant   Consultant `gorm:"constraint:OnUpdate:CASCADE,OnDelete:SET NULL;" json:"createdBy"`
	ConsultantID uint       `gorm:"index" json:"createdById"`
	AssignmentID uint       `gorm:"index" json:"assignmentId"`

	Content   string    `gorm:"type:text" json:"content"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}
