package dto

import (
	"time"

	"github.com/SneatX/phillips_go/internal/core/domain"
)

type CarrierResponse struct {
	ID         uint              `json:"id"`
	Name       string            `json:"name"`
	Email      string            `json:"email"`
	Phone      string            `json:"phone"`
	AdjusterID *uint             `json:"adjusterId"`
	Adjuster   *AdjusterResponse `json:"adjuster"`
	CreatedAt  time.Time         `json:"createdAt"`
	UpdatedAt  time.Time         `json:"updatedAt"`
}

func FromDomainCarrier(carrier domain.Carrier) CarrierResponse {
	response := CarrierResponse{
		ID:         carrier.ID,
		Name:       carrier.Name,
		Email:      carrier.Email,
		Phone:      carrier.Phone,
		AdjusterID: carrier.AdjusterID,
		CreatedAt:  carrier.CreatedAt,
		UpdatedAt:  carrier.UpdatedAt,
	}

	if carrier.AdjusterID != nil && carrier.Adjuster.ID != 0 {
		adjusterResponse := AdjusterResponse{
			ID:        carrier.Adjuster.ID,
			Name:      carrier.Adjuster.Name,
			Email:     carrier.Adjuster.Email,
			Phone:     carrier.Adjuster.Phone,
			CreatedAt: carrier.Adjuster.CreatedAt,
			UpdatedAt: carrier.Adjuster.UpdatedAt,
		}
		response.Adjuster = &adjusterResponse
	}

	return response
}

type CarrierListResponse struct {
	Results []CarrierResponse `json:"results"`
	Total   int               `json:"total"`
	Message string            `json:"message"`
}

func NewCarrierListResponse(carriers []*domain.Carrier, message string) CarrierListResponse {
	carriersResponse := make([]CarrierResponse, len(carriers))
	for i, carrier := range carriers {
		carriersResponse[i] = FromDomainCarrier(*carrier)
	}
	return CarrierListResponse{
		Results: carriersResponse,
		Total:   len(carriers),
		Message: message,
	}
}

type CreateCarrierRequest struct {
	Name       string `json:"name" binding:"required"`
	Email      string `json:"email" binding:"required"`
	Phone      string `json:"phone" binding:"required"`
	AdjusterID *uint  `json:"adjusterId"`
}

func ToDomainCarrier(r *CreateCarrierRequest) *domain.Carrier {
	return &domain.Carrier{
		Name:       r.Name,
		Email:      r.Email,
		Phone:      r.Phone,
		AdjusterID: r.AdjusterID,
	}
}

type UpdateCarrierRequest struct {
	Name       string `json:"name"`
	Email      string `json:"email"`
	Phone      string `json:"phone"`
	AdjusterID *uint  `json:"adjusterId"`
}

func ToUpdateDomainCarrier(r *UpdateCarrierRequest, adjusterID uint) *domain.Carrier {
	return &domain.Carrier{
		ID:         adjusterID,
		Name:       r.Name,
		Email:      r.Email,
		Phone:      r.Phone,
		AdjusterID: r.AdjusterID,
	}
}
