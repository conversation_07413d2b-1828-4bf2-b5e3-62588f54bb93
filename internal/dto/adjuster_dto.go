package dto

import (
	"time"

	"github.com/SneatX/phillips_go/internal/core/domain"
)

type AdjusterResponse struct {
	ID        uint              `json:"id"`
	Name      string            `json:"name"`
	Email     string            `json:"email"`
	Phone     string            `json:"phone"`
	Carriers  []CarrierResponse `json:"carriers"`
	CreatedAt time.Time         `json:"createdAt"`
	UpdatedAt time.Time         `json:"updatedAt"`
}

func FromDomainAdjuster(adjuster domain.Adjuster) AdjusterResponse {
	response := AdjusterResponse{
		ID:        adjuster.ID,
		Name:      adjuster.Name,
		Email:     adjuster.Email,
		Phone:     adjuster.Phone,
		CreatedAt: adjuster.CreatedAt,
		UpdatedAt: adjuster.UpdatedAt,
	}

	response.Carriers = make([]CarrierResponse, len(adjuster.Carriers))
	for i, carrier := range adjuster.Carriers {
		response.Carriers[i] = FromDomainCarrier(*carrier)
	}

	return response
}

type AdjusterListResponse struct {
	Results []AdjusterResponse `json:"results"`
	Total   int                `json:"total"`
	Message string             `json:"message"`
}

func NewAdjusterListResponse(adjusters []*domain.Adjuster, message string) AdjusterListResponse {
	adjustersResponse := make([]AdjusterResponse, len(adjusters))
	for i, adjuster := range adjusters {
		adjustersResponse[i] = FromDomainAdjuster(*adjuster)
	}
	return AdjusterListResponse{
		Results: adjustersResponse,
		Total:   len(adjustersResponse),
		Message: message,
	}
}

type CreateAdjusterRequest struct {
	Name  string `gorm:"size:255;not null"`
	Email string `gorm:"size:255"`
	Phone string `gorm:"size:50"`
}

func ToDomainAdjuster(r *CreateAdjusterRequest) *domain.Adjuster {
	return &domain.Adjuster{
		Name:  r.Name,
		Email: r.Email,
		Phone: r.Phone,
	}
}

type UpdateAdjusterRequest struct {
	Name  string `json:"name"`
	Email string `json:"email"`
	Phone string `json:"phone"`
}

func ToUpdateDomainAdjuster(r *UpdateAdjusterRequest, adjusterID uint) *domain.Adjuster {
	return &domain.Adjuster{
		ID:    adjusterID,
		Name:  r.Name,
		Email: r.Email,
		Phone: r.Phone,
	}
}
