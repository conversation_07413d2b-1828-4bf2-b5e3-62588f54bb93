package dto

import (
	"github.com/SneatX/phillips_go/internal/core/domain"
)

type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

type LoginResponse struct {
	User UserResponse `json:"user"`
}

func NewLoginResponse(user domain.User) LoginResponse {
	return LoginResponse{
		User: FromDomainUser(user),
	}
}

type LogoutResponse struct {
	Message string `json:"message"`
}

func NewLogoutResponse() LogoutResponse {
	return LogoutResponse{
		Message: "Successfully logged out",
	}
}
