package dto

import (
	"time"

	"github.com/SneatX/phillips_go/internal/core/domain"
)

type LocationResponse struct {
	ID         uint      `json:"id"`
	Street     string    `json:"street"`
	City       string    `json:"city"`
	State      string    `json:"state"`
	PostalCode string    `json:"postalCode"`
	Country    string    `json:"country"`
	CreatedAt  time.Time `json:"createdAt"`
	UpdatedAt  time.Time `json:"updatedAt"`
}

func FromDomainLocation(location domain.Location) LocationResponse {
	return LocationResponse{
		ID:         location.ID,
		Street:     location.Street,
		City:       location.City,
		State:      location.State,
		PostalCode: location.PostalCode,
		Country:    location.Country,
		CreatedAt:  location.CreatedAt,
		UpdatedAt:  location.UpdatedAt,
	}
}

type CreateLocationRequest struct {
	Street     string `json:"street" binding:"required"`
	City       string `json:"city" binding:"required"`
	State      string `json:"state" binding:"required"`
	PostalCode string `json:"postalCode" binding:"required"`
	Country    string `json:"country" binding:"required"`
}

func ToDomainLocation(r *CreateLocationRequest) *domain.Location {
	return &domain.Location{
		Street:     r.Street,
		City:       r.City,
		State:      r.State,
		PostalCode: r.PostalCode,
		Country:    r.Country,
	}
}

type UpdateLocationRequest struct {
	Street     string `json:"street"`
	City       string `json:"city"`
	State      string `json:"state"`
	PostalCode string `json:"postalCode"`
	Country    string `json:"country"`
}

func ToUpdateDomainLocation(r *UpdateLocationRequest, locationID uint) *domain.Location {
	return &domain.Location{
		ID:         locationID,
		Street:     r.Street,
		City:       r.City,
		State:      r.State,
		PostalCode: r.PostalCode,
		Country:    r.Country,
	}
}
