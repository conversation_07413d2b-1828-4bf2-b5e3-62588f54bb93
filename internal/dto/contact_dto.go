package dto

import (
	"time"

	"github.com/SneatX/phillips_go/internal/core/domain"
)

type ContactResponse struct {
	ID        uint      `json:"id"`
	Name      string    `json:"name"`
	Phone     string    `json:"phone"`
	Email     string    `json:"email"`
	JobTitle  string    `json:"jobTitle"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

func FromDomainContact(contact domain.Contact) ContactResponse {
	return ContactResponse{
		ID:        contact.ID,
		Name:      contact.Name,
		Phone:     contact.Phone,
		Email:     contact.Email,
		JobTitle:  contact.JobTitle,
		CreatedAt: contact.CreatedAt,
		UpdatedAt: contact.UpdatedAt,
	}
}

type CreateContactRequest struct {
	Name     string `json:"name" binding:"required"`
	Phone    string `json:"phone"`
	Email    string `json:"email"`
	JobTitle string `json:"jobTitle"`
}

func ToDomainContact(r *CreateContactRequest) *domain.Contact {
	return &domain.Contact{
		Name:     r.Name,
		Phone:    r.Phone,
		Email:    r.<PERSON>ail,
		JobTitle: r.<PERSON>,
	}
}

type UpdateContactRequest struct {
	Name     string `json:"name"`
	Phone    string `json:"phone"`
	Email    string `json:"email"`
	JobTitle string `json:"jobTitle"`
}

func ToUpdateDomainContact(r *UpdateContactRequest, contactID uint) *domain.Contact {
	return &domain.Contact{
		ID:       contactID,
		Name:     r.Name,
		Phone:    r.Phone,
		Email:    r.Email,
		JobTitle: r.JobTitle,
	}
}
