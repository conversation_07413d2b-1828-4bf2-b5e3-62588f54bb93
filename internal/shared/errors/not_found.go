package errors

import "errors"

func NewNotFoundError(message string, originalErr ...error) AppError {
	var wrapped error
	if len(originalErr) > 0 {
		wrapped = originalErr[0]
	}
	return &baseAppError{
		Msg:     message,
		ErrCode: "RESOURCE_NOT_FOUND",
		ErrType: TypeNotFound,
		Status:  404,
		Wrapped: wrapped,
	}
}

func IsNotFoundError(err error) bool {
	var appErr AppError
	return errors.As(err, &appErr) && appErr.Type() == TypeNotFound
}
