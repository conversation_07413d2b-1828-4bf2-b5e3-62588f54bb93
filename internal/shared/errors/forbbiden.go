package errors

import "errors"

func NewForbiddenError(message string, originalErr ...error) AppError {
	var wrapped error
	if len(originalErr) > 0 {
		wrapped = originalErr[0]
	}
	return &baseAppError{
		Msg:     message,
		ErrCode: "PERMISSION_DENIED",
		ErrType: TypeForbidden,
		Status:  403,
		Wrapped: wrapped,
	}
}

func IsForbiddenError(err error) bool {
	var appErr AppError
	return errors.As(err, &appErr) && appErr.Type() == TypeForbidden
}
