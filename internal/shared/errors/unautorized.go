package errors

import "errors"

func NewUnauthorizedError(message string, originalErr ...error) AppError {
	var wrapped error
	if len(originalErr) > 0 {
		wrapped = originalErr[0]
	}
	return &baseAppError{
		Msg:     message,
		ErrCode: "AUTHENTICATION_REQUIRED",
		ErrType: TypeUnauthorized,
		Status:  401,
		Wrapped: wrapped,
	}
}

func IsUnauthorizedError(err error) bool {
	var appErr AppError
	return errors.As(err, &appErr) && appErr.Type() == TypeUnauthorized
}
