package jwt

import (
	"errors"
	"fmt"
	"time"

	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/golang-jwt/jwt/v5"
)

type JWTManager struct {
	secretKey     string
	tokenDuration time.Duration
	cookieName    string
}

type UserClaims struct {
	jwt.RegisteredClaims
	UserID uint   `json:"user_id"`
	Email  string `json:"email"`
	Role   string `json:"role"`
}

func NewJWTManager(secretKey string, tokenDuration time.Duration, cookieName string) *JWTManager {
	return &JWTManager{
		secretKey:     secretKey,
		tokenDuration: tokenDuration,
		cookieName:    cookieName,
	}
}

func (m *JWTManager) GenerateToken(userID uint, email, role string) (string, error) {
	claims := UserClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(m.tokenDuration)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Subject:   fmt.Sprintf("%d", userID),
			Issuer:    "Phillips api",
		},
		UserID: userID,
		Email:  email,
		Role:   role,
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	signedToken, err := token.SignedString([]byte(m.secretKey))
	if err != nil {
		return "", appErrors.NewInternalError("failed to sign token", err)
	}
	return signedToken, nil
}

func (m *JWTManager) VerifyToken(accessToken string) (*UserClaims, error) {
	token, err := jwt.ParseWithClaims(accessToken, &UserClaims{}, func(token *jwt.Token) (any, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, appErrors.NewUnauthorizedError(fmt.Sprintf("unexpected signing method: %v", token.Header["alg"]))
		}
		return []byte(m.secretKey), nil
	})

	if err != nil {
		switch {
		case errors.Is(err, jwt.ErrTokenMalformed):
			return nil, appErrors.NewUnauthorizedError("malformed token")
		case errors.Is(err, jwt.ErrTokenExpired):
			return nil, appErrors.NewUnauthorizedError("token has expired")
		case errors.Is(err, jwt.ErrTokenNotValidYet):
			return nil, appErrors.NewUnauthorizedError("token is not yet valid")
		case errors.Is(err, jwt.ErrTokenSignatureInvalid):
			return nil, appErrors.NewUnauthorizedError("invalid token signature")
		default:
			return nil, appErrors.NewUnauthorizedError("invalid token", err)
		}
	}

	claims, ok := token.Claims.(*UserClaims)
	if !ok {
		return nil, appErrors.NewUnauthorizedError("invalid token claims")
	}

	if !token.Valid {
		return nil, appErrors.NewUnauthorizedError("token is not valid")
	}

	return claims, nil
}

func (m *JWTManager) GetTokenDuration() time.Duration {
	return m.tokenDuration
}

func (m *JWTManager) GetCookieName() string {
	return m.cookieName
}
